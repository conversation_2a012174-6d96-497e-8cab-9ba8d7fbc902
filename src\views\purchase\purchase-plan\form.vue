<template>
	<el-dialog :title="form.id ? '编辑采购计划' : '新增采购计划'" v-model="visible" :close-on-click-modal="false" draggable width="800">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="120px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="采购单位名称" prop="unitName">
						<el-input v-model="form.unitName" placeholder="请输入采购单位名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="采购单位类型" prop="unitType">
						<el-select v-model="form.unitType" placeholder="请选择采购单位类型" style="width: 100%">
							<el-option label="学校" value="school" />
							<el-option label="企业" value="enterprise" />
							<el-option label="政府机关" value="government" />
							<el-option label="医院" value="hospital" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="采购计划编号" prop="planNumber">
						<el-input v-model="form.planNumber" placeholder="系统自动生成" readonly />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="采购方式" prop="purchaseMethod">
						<el-select v-model="form.purchaseMethod" placeholder="请选择采购方式" style="width: 100%">
							<el-option label="在线采购" value="online" />
							<el-option label="线下采购" value="offline" />
							<el-option label="招标采购" value="bidding" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="计划金额" prop="planAmount">
						<el-input v-model="form.planAmount" placeholder="请输入计划金额">
							<template #append>元</template>
						</el-input>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="建设时间" prop="buildingTime">
						<el-date-picker
							type="datetime"
							placeholder="请选择建设时间"
							v-model="form.buildingTime"
							:value-format="dateTimeStr"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="配置状态" prop="configStatus">
						<el-select v-model="form.configStatus" placeholder="请选择配置状态" style="width: 100%">
							<el-option label="配置完成" value="completed" />
							<el-option label="配置中" value="pending" />
							<el-option label="配置失败" value="failed" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="计划状态" prop="planStatus">
						<el-select v-model="form.planStatus" placeholder="请选择计划状态" style="width: 100%">
							<el-option label="草稿" value="draft" />
							<el-option label="已提交" value="submitted" />
							<el-option label="已绑定" value="bound" />
							<el-option label="已取消" value="cancelled" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="预计开始时间" prop="expectedStartTime">
						<el-date-picker
							type="date"
							placeholder="请选择预计开始时间"
							v-model="form.expectedStartTime"
							:value-format="dateStr"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="预计结束时间" prop="expectedEndTime">
						<el-date-picker
							type="date"
							placeholder="请选择预计结束时间"
							v-model="form.expectedEndTime"
							:value-format="dateStr"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="采购需求描述" prop="requirementDescription">
						<el-input
							type="textarea"
							v-model="form.requirementDescription"
							placeholder="请输入采购需求描述"
							:rows="3"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="备注" prop="remark">
						<el-input
							type="textarea"
							v-model="form.remark"
							placeholder="请输入备注信息"
							:rows="2"
						/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="PurchasePlanDialog">
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 定义字典
const dateStr = 'YYYY-MM-DD';
const dateTimeStr = 'YYYY-MM-DD HH:mm:ss';

// 提交表单数据
const form = reactive({
	id: '',
	unitName: '',
	unitType: '',
	planNumber: '',
	purchaseMethod: '',
	planAmount: '',
	buildingTime: '',
	configStatus: 'pending',
	planStatus: 'draft',
	expectedStartTime: '',
	expectedEndTime: '',
	requirementDescription: '',
	remark: ''
});

// 定义校验规则
const dataRules = ref({
	unitName: [{ required: true, message: '采购单位名称不能为空', trigger: 'blur' }],
	unitType: [{ required: true, message: '采购单位类型不能为空', trigger: 'change' }],
	purchaseMethod: [{ required: true, message: '采购方式不能为空', trigger: 'change' }],
	planAmount: [
		{ required: true, message: '计划金额不能为空', trigger: 'blur' },
		{ pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
	],
	configStatus: [{ required: true, message: '配置状态不能为空', trigger: 'change' }],
	planStatus: [{ required: true, message: '计划状态不能为空', trigger: 'change' }],
	expectedStartTime: [{ required: true, message: '预计开始时间不能为空', trigger: 'change' }],
	expectedEndTime: [{ required: true, message: '预计结束时间不能为空', trigger: 'change' }]
});

// 生成采购计划编号
const generatePlanNumber = () => {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0');
	const day = String(now.getDate()).padStart(2, '0');
	const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
	return `CGH+${year}${month}${day}${random}`;
};

// 打开弹窗
const openDialog = (id?: string) => {
	visible.value = true;

	// 重置表单数据
	Object.assign(form, {
		id: '',
		unitName: '',
		unitType: '',
		planNumber: generatePlanNumber(),
		purchaseMethod: '',
		planAmount: '',
		buildingTime: '',
		configStatus: 'pending',
		planStatus: 'draft',
		expectedStartTime: '',
		expectedEndTime: '',
		requirementDescription: '',
		remark: ''
	});

	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 如果是编辑模式，模拟获取数据
	if (id) {
		form.id = id;
		getPurchasePlanData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	// 验证时间逻辑
	if (form.expectedStartTime && form.expectedEndTime) {
		if (new Date(form.expectedStartTime) >= new Date(form.expectedEndTime)) {
			useMessage().error('预计开始时间不能晚于或等于预计结束时间');
			return false;
		}
	}

	try {
		loading.value = true;

		// 模拟API调用
		await new Promise(resolve => setTimeout(resolve, 1000));

		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error('操作失败');
	} finally {
		loading.value = false;
	}
};

// 模拟获取采购计划数据
const getPurchasePlanData = (id: string) => {
	loading.value = true;

	// 模拟数据
	const mockData = {
		'1': {
			id: '1',
			unitName: '阳光小学(学生) - 幼儿园班级',
			unitType: 'school',
			planNumber: 'CGH+2025070072',
			purchaseMethod: 'online',
			planAmount: '50000.00',
			buildingTime: '2025-07-26 17:34:50',
			configStatus: 'completed',
			planStatus: 'submitted',
			expectedStartTime: '2025-08-01',
			expectedEndTime: '2025-08-31',
			requirementDescription: '幼儿园班级食堂设备采购，包括厨房设备、餐具等。',
			remark: '优先考虑环保材料'
		},
		'2': {
			id: '2',
			unitName: '阳光小学(学生)',
			unitType: 'school',
			planNumber: 'CGH+2025070071',
			purchaseMethod: 'offline',
			planAmount: '80000.00',
			buildingTime: '2025-07-26 17:36:50',
			configStatus: 'completed',
			planStatus: 'bound',
			expectedStartTime: '2025-07-15',
			expectedEndTime: '2025-08-15',
			requirementDescription: '学校食堂整体设备更新改造项目。',
			remark: '需要符合食品安全标准'
		}
	};

	setTimeout(() => {
		const data = mockData[id as keyof typeof mockData];
		if (data) {
			Object.assign(form, data);
		}
		loading.value = false;
	}, 500);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
