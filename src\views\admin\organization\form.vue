<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="机构名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入机构名称" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="机构图片" prop="images">
						<Image2 v-model="imagesUrl"></Image2>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="机构简介" prop="introduce">
						<el-input type="textarea" v-model="form.introduce" placeholder="请输入机构简介" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="工作制度" prop="workSystem">
						<el-input type="textarea" v-model="form.workSystem" placeholder="请输入工作制度" />
					</el-form-item>
				</el-col>

        <el-col :span="24" class="mb20">
          <el-form-item label="成立时间" prop="establishTime">
            <el-date-picker type="date" placeholder="请选择成立时间" v-model="form.establishTime" :value-format="dateStr"></el-date-picker>
          </el-form-item>
        </el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="机构技术人员" prop="userOne" label-width="200px">
            <el-input-number style="width: 200px" :min="1" :max="1000" v-model="form.userOne" placeholder="请输入人数"/>&nbsp;&nbsp;人
					</el-form-item>
				</el-col>
        <el-col :span="24" class="mb20">
          <el-form-item label="行业领域专家" prop="userTwo" label-width="200px">
            <el-input-number style="width: 200px" :min="1" :max="1000" v-model="form.userTwo" placeholder="请输入人数"/>&nbsp;&nbsp;人
          </el-form-item>
        </el-col>

        <el-col :span="24" class="mb20">
          <el-form-item label="行业单位骨干" prop="userThree" label-width="200px">
            <el-input-number style="width: 200px" :min="1" :max="1000" v-model="form.userThree" placeholder="请输入人数"/>&nbsp;&nbsp;人
          </el-form-item>
        </el-col>

        <el-col :span="24" class="mb20">
          <el-form-item label="处室负责人" prop="userFour" label-width="200px">
            <el-input-number style="width: 200px" :min="1" :max="1000" v-model="form.userFour" placeholder="请输入人数"/>&nbsp;&nbsp;人
          </el-form-item>
        </el-col>

        <el-col :span="24" class="mb20">
          <el-form-item label="处室工作人员" prop="userFive" label-width="200px">
            <el-input-number style="width: 200px" :min="1" :max="1000" v-model="form.userFive" placeholder="请输入人数"/>&nbsp;&nbsp;人
          </el-form-item>
        </el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="OrganizationDialog">
import { useMessage } from '/@/hooks/message';
import { addObj, getObj, putObj } from '/@/api/admin/organization';
import Image2 from '/@/components/Upload/Image2.vue';

const emit = defineEmits(['refresh']);
const imagesUrl = ref<any>([]);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const dateStr = 'YYYY-MM-DD';
// 提交表单数据
const form = reactive({
	id: '',
	name: '',
	images: '',
	introduce: '',
	workSystem: '',
	establishTime: '',
  useOne: '',
  useTwo: '',
  useThree: '',
  useFour: '',
  useFive: ''
});

// 定义校验规则
const dataRules = ref({
	name: [{ required: true, message: '机构名称不能为空', trigger: 'blur' }],
	introduce: [{ required: true, message: '机构简介不能为空', trigger: 'blur' }],
	workSystem: [{ required: true, message: '工作制度不能为空', trigger: 'blur' }],
	establishTime: [{ required: true, message: '成立时间不能为空', trigger: 'blur' }],
  userOne: [{ required: true, message: '人数不能为空', trigger: 'blur' }],
  userTwo: [{ required: true, message: '人数不能为空', trigger: 'blur' }],
  userThree: [{ required: true, message: '人数不能为空', trigger: 'blur' }],
  userFour: [{ required: true, message: '人数不能为空', trigger: 'blur' }],
  userFive: [{ required: true, message: '人数不能为空', trigger: 'blur' }]
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 获取organization信息
	if (id) {
		form.id = id;
		getorganizationData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		if (imagesUrl.value.length) {
			form.images = imagesUrl.value.join();
		}
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getorganizationData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
      imagesUrl.value = res.data.images.split(',');
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
