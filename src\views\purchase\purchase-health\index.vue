<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="人员姓名" prop="name">
            <el-input placeholder="请输入人员姓名" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="健康状态" prop="healthStatus">
            <el-select placeholder="请选择健康状态" v-model="state.queryForm.healthStatus" clearable>
              <el-option label="健康" value="healthy" />
              <el-option label="异常" value="abnormal" />
              <el-option label="待检查" value="pending" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_health_add'"> 新 增 </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin_health_del'" @click="handleDelete(selectObjs)"> 删除 </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'admin_health_export'" @exportExcel="exportExcel" class="ml10 mr20" style="float: right" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table
        :data="state.dataList"
        v-loading="state.loading"
        border
        :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle"
        @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column prop="name" label="人员姓名" show-overflow-tooltip />
        <el-table-column prop="employeeId" label="员工编号" show-overflow-tooltip />
        <el-table-column prop="department" label="所属部门" show-overflow-tooltip />
        <el-table-column prop="position" label="职位" show-overflow-tooltip />
        <el-table-column prop="healthStatus" label="健康状态" width="100">
          <template #default="scope">
            <el-tag :type="getHealthStatusType(scope.row.healthStatus)">
              {{ getHealthStatusText(scope.row.healthStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastCheckDate" label="最近体检日期" show-overflow-tooltip />
        <el-table-column prop="nextCheckDate" label="下次体检日期" show-overflow-tooltip />
        <el-table-column prop="healthCertificate" label="健康证状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.healthCertificate === 'valid' ? 'success' : 'danger'">
              {{ scope.row.healthCertificate === "valid" ? "有效" : "过期" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="certificateExpiry" label="健康证到期日期" show-overflow-tooltip />
        <el-table-column prop="temperature" label="当日体温(°C)" width="120" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.id)">编辑 </el-button>
            <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="purchaseHealth">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { useMessage, useMessageBox } from "/@/hooks/message";

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 写死的数据
const mockData = [
  {
    id: "1",
    name: "张三",
    employeeId: "EMP001",
    department: "食材配送部",
    position: "配送员",
    healthStatus: "healthy",
    lastCheckDate: "2024-07-15",
    nextCheckDate: "2025-01-15",
    healthCertificate: "valid",
    certificateExpiry: "2025-03-20",
    temperature: "36.5",
    createTime: "2024-01-10 09:30:00",
  },
  {
    id: "2",
    name: "李四",
    employeeId: "EMP002",
    department: "食材配送部",
    position: "配送主管",
    healthStatus: "healthy",
    lastCheckDate: "2024-07-20",
    nextCheckDate: "2025-01-20",
    healthCertificate: "valid",
    certificateExpiry: "2025-04-15",
    temperature: "36.3",
    createTime: "2024-01-15 10:15:00",
  },
  {
    id: "3",
    name: "王五",
    employeeId: "EMP003",
    department: "食材配送部",
    position: "配送员",
    healthStatus: "pending",
    lastCheckDate: "2024-01-10",
    nextCheckDate: "2024-07-10",
    healthCertificate: "expired",
    certificateExpiry: "2024-06-30",
    temperature: "36.8",
    createTime: "2024-01-05 14:20:00",
  },
  {
    id: "4",
    name: "赵六",
    employeeId: "EMP004",
    department: "食材配送部",
    position: "配送员",
    healthStatus: "abnormal",
    lastCheckDate: "2024-07-25",
    nextCheckDate: "2024-08-25",
    healthCertificate: "valid",
    certificateExpiry: "2025-02-10",
    temperature: "37.2",
    createTime: "2024-01-20 11:45:00",
  },
  {
    id: "5",
    name: "孙七",
    employeeId: "EMP005",
    department: "食材配送部",
    position: "仓库管理员",
    healthStatus: "healthy",
    lastCheckDate: "2024-07-18",
    nextCheckDate: "2025-01-18",
    healthCertificate: "valid",
    certificateExpiry: "2025-05-08",
    temperature: "36.4",
    createTime: "2024-01-25 16:30:00",
  },
];

// 模拟API调用
const mockFetchList = async (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.includes(params.name));
      }

      if (params.healthStatus) {
        filteredData = filteredData.filter((item) => item.healthStatus === params.healthStatus);
      }

      resolve({
        code: 0,
        data: {
          records: filteredData,
          total: filteredData.length,
          size: params.size || 10,
          current: params.current || 1,
        },
      });
    }, 300);
  });
};

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: mockFetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 健康状态标签类型
const getHealthStatusType = (status: string) => {
  switch (status) {
    case "healthy":
      return "success";
    case "abnormal":
      return "danger";
    case "pending":
      return "warning";
    default:
      return "";
  }
};

// 健康状态文本
const getHealthStatusText = (status: string) => {
  switch (status) {
    case "healthy":
      return "健康";
    case "abnormal":
      return "异常";
    case "pending":
      return "待检查";
    default:
      return "未知";
  }
};

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields();
  // 清空多选
  selectObjs.value = [];
  getDataList();
};

// 导出excel
const exportExcel = () => {
  downBlobFile("/admin/health/export", Object.assign(state.queryForm, { ids: selectObjs }), "health.xlsx");
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm("此操作将永久删除");
  } catch {
    return;
  }

  try {
    // 模拟删除操作
    useMessage().success("删除成功");
    getDataList();
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};

// 初始化加载数据
onMounted(() => {
  getDataList();
});
</script>
<style lang="scss" scoped>
.img-box {
  display: flex;
  flex-direction: row;

  .el-image {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
}
</style>
