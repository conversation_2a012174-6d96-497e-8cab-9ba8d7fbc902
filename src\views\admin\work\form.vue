<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="900">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="工作主题" prop="title">
						<el-input v-model="form.title" placeholder="请输入工作主题" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="工作类型" prop="type">
						<el-select v-model="form.type" placeholder="请选择工作类型">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in work_type"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="所属机构" prop="organizationId">
						<el-select v-model="form.organizationId" placeholder="请选择所属机构">
							<el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in orgList" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="主要内容" prop="content">
						<editor v-if="visible" v-model:get-html="form.content" placeholder="请输入主要内容"></editor>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="附件" prop="fileUrl">
            <UploadFile v-model="form.fileUrl" :limit="1"></UploadFile>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="OrganizationWorkDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { addObj, getObj, putObj } from '/@/api/admin/work';
import { list as organList } from '/@/api/admin/organization';
import UploadFile from '/@/components/Upload/UploadFile.vue';

const emit = defineEmits(['refresh']);
const { work_type } = useDict('work_type');
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const orgList = ref<any[]>([]);
// 提交表单数据
const form = reactive({
	id: '',
	title: '',
	type: '',
	organizationId: '',
	content: '',
	fileUrl: '',
	describes: '',
});

// 定义校验规则
const dataRules = ref({
	title: [{ required: true, message: '主题不能为空', trigger: 'blur' }],
	type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
	organizationId: [{ required: true, message: '机构ID不能为空', trigger: 'blur' }],
	content: [{ required: true, message: '主要内容不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	await getOrgList();
	// 获取organizationWork信息
	if (id) {
		form.id = id;
		getorganizationWorkData(id);
	}
};

// 机构数据
const getOrgList = async () => {
	organList().then((res) => {
		orgList.value = res.data;
	});
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getorganizationWorkData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
      form.type = res.data.type + '';
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
