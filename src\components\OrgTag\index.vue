<template>
	<div>
		<template v-for="(item, index) in props.options" :key="index * 2">
			<template v-if="values.includes(item.id || item)">
				<span v-if="item.elTagType == 'default' || item.elTagType == ''" :class="item.elTagClass">{{item.name || item}}</span>
				<el-tag
					v-else
					:disable-transitions="true"
					:type="item.elTagType === 'primary' ? 'primary' : item.elTagType"
					:class="item.elTagClass"
					>{{ item.name || item }}</el-tag>
			</template>
		</template>
	</div>
</template>

<script setup lang="ts" name="org-tag">
import { computed } from 'vue';

const props = defineProps({
	// 数据
	options: {
		type: Array as any,
		default: null,
	},
  type: String,
	// 当前的值
	value: [Number, String, Array],
});

const values = computed(() => {
	if (props.value !== null && typeof props.value !== 'undefined') {
		return Array.isArray(props.value) ? props.value : [String(props.value)];
	} else {
		return [];
	}
});
</script>

<style scoped>
.el-tag + .el-tag {
	margin-left: 10px;
}
</style>
