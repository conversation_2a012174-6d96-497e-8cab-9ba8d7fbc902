<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="商品名称" prop="name">
						<el-input placeholder="请输入商品名称" v-model="state.queryForm.name" />
					</el-form-item>
					<el-form-item label="商品分类" prop="category">
						<el-select placeholder="请选择商品分类" v-model="state.queryForm.category" clearable>
							<el-option label="蔬菜类" value="vegetables" />
							<el-option label="肉类" value="meat" />
							<el-option label="水果类" value="fruits" />
							<el-option label="调料类" value="seasoning" />
							<el-option label="粮食类" value="grain" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_goods_add'">
						新 增
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin_goods_del'" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'admin_goods_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column prop="name" label="商品名称" show-overflow-tooltip />
				<el-table-column prop="images" label="商品图片" width="180">
					<template #default="scope">
						<div class="img-box" v-if="scope.row.images">
							<el-image
								v-for="m in scope.row.images.split(',')"
								:key="m"
								:src="m.includes('http') ? m : baseURL + m"
								:preview-src-list="[m.includes('http') ? m : baseURL + m]"
								fit="cover"
								:preview-teleported="true"
								:hide-on-click-modal="true"
							></el-image>
						</div>
						<div class="noPic" v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column prop="category" label="商品分类" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="getCategoryType(scope.row.category)">{{ getCategoryName(scope.row.category) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="unit" label="计量单位" show-overflow-tooltip />
				<el-table-column prop="price" label="单价(元)" show-overflow-tooltip>
					<template #default="scope">
						<span class="price">¥{{ scope.row.price }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="stock" label="库存数量" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="scope.row.stock > 10 ? 'success' : scope.row.stock > 0 ? 'warning' : 'danger'">
							{{ scope.row.stock }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="supplier" label="供应商" show-overflow-tooltip />
				<el-table-column prop="shelfLife" label="保质期(天)" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
							{{ scope.row.status === 'active' ? '上架' : '下架' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" text type="primary" v-auth="'admin_goods_edit'" @click="formDialogRef.openDialog(scope.row.id)"
							>编辑
						</el-button>
						<el-button icon="delete" text type="primary" v-auth="'admin_goods_del'" @click="handleDelete([scope.row.id])">删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="goodsList">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 写死的食材商品数据
const mockGoodsData = [
	{
		id: '1',
		name: '新鲜白菜',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'vegetables',
		unit: '斤',
		price: '3.50',
		stock: 150,
		supplier: '绿色农场',
		shelfLife: 7,
		status: 'active',
		createTime: '2024-01-15 10:30:00'
	},
	{
		id: '2',
		name: '优质猪肉',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'meat',
		unit: '斤',
		price: '28.00',
		stock: 50,
		supplier: '优质肉类供应商',
		shelfLife: 3,
		status: 'active',
		createTime: '2024-01-15 09:20:00'
	},
	{
		id: '3',
		name: '新鲜苹果',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'fruits',
		unit: '斤',
		price: '8.80',
		stock: 80,
		supplier: '果园直供',
		shelfLife: 15,
		status: 'active',
		createTime: '2024-01-14 16:45:00'
	},
	{
		id: '4',
		name: '生抽酱油',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'seasoning',
		unit: '瓶',
		price: '12.50',
		stock: 5,
		supplier: '调料批发商',
		shelfLife: 365,
		status: 'active',
		createTime: '2024-01-14 14:20:00'
	},
	{
		id: '5',
		name: '优质大米',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'grain',
		unit: '袋',
		price: '45.00',
		stock: 0,
		supplier: '粮食供应商',
		shelfLife: 180,
		status: 'inactive',
		createTime: '2024-01-13 11:10:00'
	},
	{
		id: '6',
		name: '新鲜土豆',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'vegetables',
		unit: '斤',
		price: '2.80',
		stock: 200,
		supplier: '绿色农场',
		shelfLife: 30,
		status: 'active',
		createTime: '2024-01-13 08:30:00'
	},
	{
		id: '7',
		name: '新鲜鸡蛋',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'meat',
		unit: '个',
		price: '1.20',
		stock: 300,
		supplier: '养殖场直供',
		shelfLife: 21,
		status: 'active',
		createTime: '2024-01-12 15:20:00'
	},
	{
		id: '8',
		name: '香蕉',
		images: 'https://img.yzcdn.cn/vant/cat.jpeg',
		category: 'fruits',
		unit: '斤',
		price: '6.50',
		stock: 12,
		supplier: '果园直供',
		shelfLife: 5,
		status: 'active',
		createTime: '2024-01-12 13:45:00'
	}
];

// 模拟获取数据的方法
const fetchMockList = async (params: any) => {
	return new Promise((resolve) => {
		setTimeout(() => {
			let filteredData = [...mockGoodsData];

			// 根据搜索条件过滤数据
			if (params.name) {
				filteredData = filteredData.filter(item =>
					item.name.toLowerCase().includes(params.name.toLowerCase())
				);
			}

			if (params.category) {
				filteredData = filteredData.filter(item => item.category === params.category);
			}

			// 分页处理
			const current = params.current || 1;
			const size = params.size || 10;
			const start = (current - 1) * size;
			const end = start + size;
			const records = filteredData.slice(start, end);

			resolve({
				data: {
					records,
					total: filteredData.length,
					current,
					size
				}
			});
		}, 300);
	});
};

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchMockList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 获取分类名称
const getCategoryName = (category: string) => {
	const categoryMap: Record<string, string> = {
		vegetables: '蔬菜类',
		meat: '肉类',
		fruits: '水果类',
		seasoning: '调料类',
		grain: '粮食类'
	};
	return categoryMap[category] || category;
};

// 获取分类标签类型
const getCategoryType = (category: string) => {
	const typeMap: Record<string, string> = {
		vegetables: 'success',
		meat: 'danger',
		fruits: 'warning',
		seasoning: 'info',
		grain: 'primary'
	};
	return typeMap[category] || '';
};

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/goods/export', Object.assign(state.queryForm, { ids: selectObjs }), 'goods.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		// 模拟删除操作
		useMessage().success('删除成功');
		getDataList();
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// 初始化加载数据
onMounted(() => {
	getDataList();
});
</script>
<style lang="scss" scoped>
.img-box {
	display: flex;
	flex-direction: row;

	.el-image {
		width: 40px;
		height: 40px;
		margin-right: 10px;
	}
}

.price {
	color: #f56c6c;
	font-weight: bold;
}
</style>
