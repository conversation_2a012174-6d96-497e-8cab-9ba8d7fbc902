<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="用户姓名" prop="nickname">
            <el-input placeholder="请输入用户姓名" v-model="state.queryForm.nickname" />
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input placeholder="请输入标题" v-model="state.queryForm.title" />
          </el-form-item>
					<el-form-item label="年份" prop="year">
						<el-date-picker type="year" placeholder="请输入年份" v-model="state.queryForm.year" format="YYYY" value-format="YYYY"></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_annualReport_add'">
						新 增
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin_annualReport_del'" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'admin_annualReport_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column prop="nickname" label="用户姓名" show-overflow-tooltip />
				<el-table-column prop="year" label="年份" show-overflow-tooltip />
				<el-table-column prop="title" label="标题" show-overflow-tooltip />
				<el-table-column prop="fileUrl" label="报告文件" show-overflow-tooltip>
					<template #default="scope">
						<el-button
							v-if="scope.row.fileUrl"
							icon="download"
							text
							type="primary"
							@click="downBlobFile(scope.row.fileUrl, null, scope.row.fileUrl.substring(scope.row.fileUrl.lastIndexOf('=') + 1))"
						>
							下载
						</el-button>
						<span v-else>--</span>
					</template>
				</el-table-column>
				<el-table-column prop="createBy" label="创建人" show-overflow-tooltip />
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" text type="primary" v-auth="'admin_annualReport_edit'" @click="formDialogRef.openDialog(scope.row.id)"
							>编辑
						</el-button>
						<el-button icon="delete" text type="primary" v-auth="'admin_annualReport_del'" @click="handleDelete([scope.row.id])">删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemUserAnnualReport">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/admin/annualReport';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/annualReport/export', Object.assign(state.queryForm, { ids: selectObjs }), 'annualReport.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
