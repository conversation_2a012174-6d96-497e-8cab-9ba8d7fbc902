<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="食材名称" prop="foodName">
						<el-input placeholder="请输入食材名称" v-model="state.queryForm.foodName" />
					</el-form-item>
					<el-form-item label="供应商" prop="supplierName">
						<el-input placeholder="请输入供应商名称" v-model="state.queryForm.supplierName" />
					</el-form-item>
					<el-form-item label="留样日期" prop="sampleDate">
						<el-date-picker
							type="date"
							placeholder="请选择留样日期"
							v-model="state.queryForm.sampleDate"
							value-format="YYYY-MM-DD"
						></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
						新增留样
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'retention_sample_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column prop="foodName" label="食材名称" show-overflow-tooltip />
				<el-table-column prop="supplierName" label="供应商" show-overflow-tooltip />
				<el-table-column prop="category" label="食材分类" show-overflow-tooltip />
				<el-table-column prop="sampleImages" label="留样图片" width="120">
					<template #default="scope">
						<div class="img-box" v-if="scope.row.sampleImages">
							<!-- <el-image
								v-for="m in scope.row.sampleImages.split(',')"
								:key="m"
								:src="m.includes('http') ? m : baseURL + m"
								:preview-src-list="[m.includes('http') ? m : baseURL + m]"
								fit="cover"
								:preview-teleported="true"
								:hide-on-click-modal="true"
							></el-image> -->
              <el-image
								:src="scope.row.sampleImages"
								:preview-src-list="[scope.row.sampleImages]"
								fit="cover"
								:preview-teleported="true"
								:hide-on-click-modal="true"
							></el-image>
						</div>
						<div class="noPic" v-else>-</div>
					</template>
				</el-table-column>
				<el-table-column prop="sampleDate" label="留样日期" show-overflow-tooltip />
				<el-table-column prop="sampleTime" label="留样时间" show-overflow-tooltip />
				<el-table-column prop="sampleWeight" label="留样重量(g)" show-overflow-tooltip />
				<el-table-column prop="storageLocation" label="存储位置" show-overflow-tooltip />
				<el-table-column prop="temperature" label="存储温度(℃)" show-overflow-tooltip />
				<el-table-column prop="expiryDate" label="保质期至" show-overflow-tooltip />
				<el-table-column prop="samplePerson" label="留样人员" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" width="80">
					<template #default="scope">
						<el-tag :type="scope.row.status === '正常' ? 'success' : scope.row.status === '已过期' ? 'danger' : 'warning'">
							{{ scope.row.status }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.id)">
							编辑
						</el-button>
						<el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="purchaseRetention">
import { BasicTableProps } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import IMG1 from '/@/assets/img1.png';
import IMG3 from '/@/assets/img3.jpg';
import IMG4 from '/@/assets/img4.png';
import IMG5 from '/@/assets/img5.png';
import IMG6 from '/@/assets/img6.png';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 写死的配餐留样数据
const mockData = [
	{
		id: '1',
		foodName: '五花肉',
		supplierName: '新鲜食材供应商',
		category: '肉类',
		sampleImages: IMG1,
		sampleDate: '2025-01-15',
		sampleTime: '08:30',
		sampleWeight: '125',
		storageLocation: '冷藏室A-01',
		temperature: '2-4',
		expiryDate: '2025-01-18',
		samplePerson: '张三',
		status: '正常'
	},
	{
		id: '2',
		foodName: '新鲜白菜',
		supplierName: '绿色蔬菜基地',
		category: '蔬菜',
		sampleImages: IMG3,
		sampleDate: '2025-01-15',
		sampleTime: '09:15',
		sampleWeight: '150',
		storageLocation: '冷藏室B-03',
		temperature: '0-2',
		expiryDate: '2025-01-17',
		samplePerson: '李四',
		status: '正常'
	},
	{
		id: '3',
		foodName: '土豆',
		supplierName: '农家直供合作社',
		category: '蔬菜',
		sampleImages: IMG6,
		sampleDate: '2025-01-14',
		sampleTime: '10:20',
		sampleWeight: '200',
		storageLocation: '常温库C-05',
		temperature: '15-18',
		expiryDate: '2025-01-21',
		samplePerson: '王五',
		status: '正常'
	},
	{
		id: '4',
		foodName: '鸡蛋',
		supplierName: '优质禽蛋供应商',
		category: '蛋类',
		sampleImages: IMG5,
		sampleDate: '2025-01-13',
		sampleTime: '07:45',
		sampleWeight: '100',
		storageLocation: '冷藏室A-08',
		temperature: '4-6',
		expiryDate: '2025-01-16',
		samplePerson: '赵六',
		status: '即将过期'
	},
	{
		id: '5',
		foodName: '牛奶',
		supplierName: '优质乳品公司',
		category: '乳制品',
		sampleImages: IMG4,
		sampleDate: '2025-01-12',
		sampleTime: '08:00',
		sampleWeight: '250',
		storageLocation: '冷藏室A-12',
		temperature: '2-4',
		expiryDate: '2025-01-15',
		samplePerson: '孙七',
		status: '已过期'
	}
];

// 模拟分页数据
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	dataList: mockData,
	loading: false,
	pagination: {
		current: 1,
		size: 10,
		total: mockData.length
	}
});

// 模拟获取数据的函数
const getDataList = () => {
	state.loading = true;
	setTimeout(() => {
		let filteredData = [...mockData];

		// 根据查询条件过滤数据
		if (state.queryForm.foodName) {
			filteredData = filteredData.filter(item =>
				item.foodName.includes(state.queryForm.foodName)
			);
		}
		if (state.queryForm.supplierName) {
			filteredData = filteredData.filter(item =>
				item.supplierName.includes(state.queryForm.supplierName)
			);
		}
		if (state.queryForm.sampleDate) {
			filteredData = filteredData.filter(item =>
				item.sampleDate === state.queryForm.sampleDate
			);
		}

		state.dataList = filteredData;
		state.pagination.total = filteredData.length;
		state.loading = false;
	}, 500);
};

// 表格样式
const tableStyle = {
	cellStyle: { textAlign: 'center' },
	headerCellStyle: { textAlign: 'center', background: '#f5f7fa' }
};

// 分页处理
const currentChangeHandle = (page: number) => {
	state.pagination.current = page;
};

const sizeChangeHandle = (size: number) => {
	state.pagination.size = size;
	state.pagination.current = 1;
};

const sortChangeHandle = () => {
	// 排序处理
};

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	useMessage().success('导出功能开发中...');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中的留样记录');
	} catch {
		return;
	}

	try {
		// 模拟删除操作
		state.dataList = state.dataList.filter(item => !ids.includes(item.id));
		state.pagination.total = state.dataList.length;
		selectObjs.value = [];
		multiple.value = true;
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error('删除失败');
	}
};

// 初始化数据
onMounted(() => {
	getDataList();
});
</script>
<style lang="scss" scoped>
.img-box {
	display: flex;
	flex-direction: row;

	.el-image {
		width: 40px;
		height: 40px;
		margin-right: 10px;
	}
}
</style>
