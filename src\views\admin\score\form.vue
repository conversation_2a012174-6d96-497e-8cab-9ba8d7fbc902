<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="600">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="用户姓名" prop="userId">
						<el-select v-model="form.userId" placeholder="请选择用户" size="large">
							<el-option v-for="item in users" :key="item.userId" :label="item.nickname" :value="item.userId" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="月份" prop="month">
						<el-date-picker type="month" placeholder="请选择月份" v-model="form.month" format="YYYYMM" value-format="YYYYMM"></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb20">
					<el-form-item label="类型" prop="type">
						<el-select v-model="form.type" placeholder="请选择类型">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in score_type"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
        <el-col :span="24" class="mb20">
          <el-form-item label="内容" prop="content">
            <el-input v-model="form.content" placeholder="请输入内容" />
          </el-form-item>
        </el-col>
				<el-col :span="24" class="mb20">
					<el-form-item v-if="form.type == 4" label="扣分" prop="score">
						<el-input-number :min="1" :max="1000" :precision="1" v-model="form.score" placeholder="请输入分数"></el-input-number>
					</el-form-item>
					<el-form-item v-else label="加分" prop="score">
						<el-input-number :min="1" :max="1000" :precision="1" v-model="form.score" placeholder="请输入分数"></el-input-number>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item v-if="form.type == 1" label="处理数量" prop="dealNum">
						<el-input-number :min="1" :max="1000" v-model="form.dealNum" placeholder="请输入处理数量"></el-input-number>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="UserMonthScoreDialog">
import { listAll as userList } from '/@/api/admin/user';
import { useMessage } from '/@/hooks/message';
import { addObj, getObj, putObj } from '/@/api/admin/score';
import { useDict } from '/@/hooks/dict';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const { score_type } = useDict('score_type');
const users = ref();
// 提交表单数据
const form = reactive({
	id: '',
	userId: '',
	month: '',
	score: null,
	type: null,
	dealNum: null,
	remark: '',
});

// 定义校验规则
const dataRules = ref({
	month: [{ required: true, message: '月份不能为空', trigger: 'blur' }],
	type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
  score: [{ required: true, message: '分数不能为空', trigger: 'blur' }],
	dealNum: [{ required: true, message: '处理数量不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	form.id = '';

	const res = await userList();
	users.value = res.data;
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
    form.dealNum = null
	});

	// 获取userMonthScore信息
	if (id) {
		form.id = id;
		getuserMonthScoreData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.type == 4 ? (form.score = -form.score) : (form.score = form.score);
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getuserMonthScoreData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
      form.month = res.data.month.toString()
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
