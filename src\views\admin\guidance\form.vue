<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="600">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="用户姓名" prop="userId">
						<el-select v-model="form.userId" placeholder="请选择用户" size="large">
							<el-option v-for="item in users" :key="item.userId" :label="item.nickname" :value="item.userId" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="指导内容" prop="content">
						<el-input type="textarea" v-model="form.content" placeholder="请输入指导内容" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="隐患数量" prop="dangerNum">
						<el-input-number :min="1" :max="1000" v-model="form.dangerNum" placeholder="请输入隐患数量"></el-input-number>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="整改通知书" prop="fileUrl" label-width="100px">
						<UploadFile v-model="form.fileUrl" :limit="1"></UploadFile>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="月份" prop="month">
						<el-date-picker type="month" placeholder="请选择月份" v-model="form.month" format="YYYYMM" value-format="YYYYMM"></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="UserGuidanceDialog">
import { listAll as userList } from '/@/api/admin/user';
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj } from '/@/api/admin/guidance';
import UploadFile from '/@/components/Upload/UploadFile.vue';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const users = ref();
// 提交表单数据
const form = reactive({
	id: '',
	userId: '',
	content: '',
	dangerNum: 0,
	fileUrl: '',
	month: '',
});

// 定义校验规则
const dataRules = ref({
	userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
	content: [{ required: true, message: '指导内容不能为空', trigger: 'blur' }],
	dangerNum: [{ required: true, message: '隐患数量不能为空', trigger: 'blur' }],
	fileUrl: [{ required: true, message: '整改通知书不能为空', trigger: 'blur' }],
	month: [{ required: true, message: '月份不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	form.id = '';
	const res = await userList();
	users.value = res.data;
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 获取userGuidance信息
	if (id) {
		form.id = id;
		getuserGuidanceData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getuserGuidanceData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
      form.month = res.data.month.toString()
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
