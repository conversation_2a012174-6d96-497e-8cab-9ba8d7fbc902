<template>
	<el-dialog :title="form.id ? '编辑留样记录' : '新增留样记录'" v-model="visible" :close-on-click-modal="false" draggable width="800">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="120px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="12" class="mb20">
					<el-form-item label="食材名称" prop="foodName">
						<el-input v-model="form.foodName" placeholder="请输入食材名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="供应商" prop="supplierName">
						<el-input v-model="form.supplierName" placeholder="请输入供应商名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="食材分类" prop="category">
						<el-select v-model="form.category" placeholder="请选择食材分类" style="width: 100%">
							<el-option label="蔬菜" value="蔬菜"></el-option>
							<el-option label="肉类" value="肉类"></el-option>
							<el-option label="蛋类" value="蛋类"></el-option>
							<el-option label="乳制品" value="乳制品"></el-option>
							<el-option label="水产品" value="水产品"></el-option>
							<el-option label="调料" value="调料"></el-option>
							<el-option label="其他" value="其他"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="留样重量(g)" prop="sampleWeight">
						<el-input-number v-model="form.sampleWeight" :min="50" :max="1000" placeholder="请输入留样重量" style="width: 100%" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="留样图片" prop="sampleImages">
						<Image2 v-model="imagesUrl"></Image2>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="留样日期" prop="sampleDate">
						<el-date-picker type="date" placeholder="请选择留样日期" v-model="form.sampleDate" :value-format="dateStr" style="width: 100%"></el-date-picker>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="留样时间" prop="sampleTime">
						<el-time-picker v-model="form.sampleTime" placeholder="请选择留样时间" format="HH:mm" value-format="HH:mm" style="width: 100%"></el-time-picker>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="存储位置" prop="storageLocation">
						<el-input v-model="form.storageLocation" placeholder="请输入存储位置" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="存储温度(℃)" prop="temperature">
						<el-input v-model="form.temperature" placeholder="如：2-4" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="保质期至" prop="expiryDate">
						<el-date-picker type="date" placeholder="请选择保质期" v-model="form.expiryDate" :value-format="dateStr" style="width: 100%"></el-date-picker>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="留样人员" prop="samplePerson">
						<el-input v-model="form.samplePerson" placeholder="请输入留样人员姓名" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="状态" prop="status">
						<el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
							<el-option label="正常" value="正常"></el-option>
							<el-option label="即将过期" value="即将过期"></el-option>
							<el-option label="已过期" value="已过期"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" v-model="form.remark" placeholder="请输入备注信息" :rows="3" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="RetentionSampleDialog">
import { useMessage } from '/@/hooks/message';
import Image2 from '/@/components/Upload/Image2.vue';

const emit = defineEmits(['refresh']);
const imagesUrl = ref<any>([]);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const dateStr = 'YYYY-MM-DD';
// 提交表单数据
const form = reactive({
	id: '',
	foodName: '',
	supplierName: '',
	category: '',
	sampleImages: '',
	sampleDate: '',
	sampleTime: '',
	sampleWeight: 125,
	storageLocation: '',
	temperature: '',
	expiryDate: '',
	samplePerson: '',
	status: '正常',
	remark: ''
});

// 定义校验规则
const dataRules = ref({
	foodName: [{ required: true, message: '食材名称不能为空', trigger: 'blur' }],
	supplierName: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
	category: [{ required: true, message: '食材分类不能为空', trigger: 'change' }],
	sampleDate: [{ required: true, message: '留样日期不能为空', trigger: 'change' }],
	sampleTime: [{ required: true, message: '留样时间不能为空', trigger: 'change' }],
	sampleWeight: [{ required: true, message: '留样重量不能为空', trigger: 'blur' }],
	storageLocation: [{ required: true, message: '存储位置不能为空', trigger: 'blur' }],
	temperature: [{ required: true, message: '存储温度不能为空', trigger: 'blur' }],
	expiryDate: [{ required: true, message: '保质期不能为空', trigger: 'change' }],
	samplePerson: [{ required: true, message: '留样人员不能为空', trigger: 'blur' }],
	status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';
	imagesUrl.value = [];

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
		// 设置默认值
		form.sampleWeight = 125;
		form.status = '正常';
		form.sampleDate = new Date().toISOString().split('T')[0];
		form.sampleTime = new Date().toTimeString().split(' ')[0].substring(0, 5);
	});

	// 如果是编辑，获取数据
	if (id) {
		form.id = id;
		getRetentionData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		if (imagesUrl.value.length) {
			form.sampleImages = imagesUrl.value.join(',');
		}

		// 模拟保存操作
		await new Promise(resolve => setTimeout(resolve, 1000));

		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error('保存失败');
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据（编辑时）
const getRetentionData = (id: string) => {
	// 模拟获取数据
	loading.value = true;
	setTimeout(() => {
		// 这里可以根据id获取对应的数据
		const mockData = {
			id: id,
			foodName: '示例食材',
			supplierName: '示例供应商',
			category: '蔬菜',
			sampleImages: 'https://via.placeholder.com/60x60/67C23A/ffffff?text=示例',
			sampleDate: '2025-01-15',
			sampleTime: '08:30',
			sampleWeight: 125,
			storageLocation: '冷藏室A-01',
			temperature: '2-4',
			expiryDate: '2025-01-18',
			samplePerson: '张三',
			status: '正常',
			remark: '示例备注'
		};

		Object.assign(form, mockData);
		if (mockData.sampleImages) {
			imagesUrl.value = mockData.sampleImages.split(',');
		}
		loading.value = false;
	}, 500);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
