<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="用户姓名" prop="nickname">
						<el-input placeholder="请输入用户姓名" v-model="state.queryForm.nickname" />
					</el-form-item>
					<el-form-item label="类型" prop="type">
						<el-select v-model="state.queryForm.type" placeholder="请选择类型">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in score_type"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="月份" prop="month">
						<el-date-picker
							type="month"
							placeholder="请选择月份"
							v-model="state.queryForm.month"
							format="YYYYMM"
							value-format="YYYYMM"
						></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_score_add'"> 新 增 </el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin_score_del'" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'admin_score_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column prop="nickname" label="用户姓名" show-overflow-tooltip />
				<el-table-column prop="month" label="月份" show-overflow-tooltip />
        <el-table-column prop="content" label="内容" show-overflow-tooltip />
				<el-table-column prop="score" label="得分" show-overflow-tooltip />
				<el-table-column prop="type" label="类型" show-overflow-tooltip>
					<template #default="scope">
						<dict-tag :options="score_type" :value="scope.row.type"></dict-tag>
					</template>
				</el-table-column>
				<el-table-column prop="dealNum" label="处理数量" show-overflow-tooltip />
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column prop="createBy" label="创建人" show-overflow-tooltip />
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" text type="primary" v-auth="'admin_score_edit'" @click="formDialogRef.openDialog(scope.row.id)"
							>编辑
						</el-button>
						<el-button icon="delete" text type="primary" v-auth="'admin_score_del'" @click="handleDelete([scope.row.id])">删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemUserMonthScore">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObjs, fetchList } from '/@/api/admin/score';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典
const { score_type } = useDict('score_type');
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/score/export', Object.assign(state.queryForm, { ids: selectObjs }), 'score.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
