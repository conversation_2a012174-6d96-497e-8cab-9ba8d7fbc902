<template>
	<el-dialog :title="form.id ? '编辑分类' : '新增分类'" v-model="visible" :close-on-click-modal="false" draggable width="600">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="12" class="mb20">
					<el-form-item label="分类名称" prop="categoryName">
						<el-input v-model="form.categoryName" placeholder="请输入分类名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="分类编码" prop="categoryCode">
						<el-input v-model="form.categoryCode" placeholder="请输入分类编码" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="父级分类" prop="parentId">
						<el-select v-model="form.parentId" placeholder="请选择父级分类" clearable style="width: 100%">
							<el-option label="无（顶级分类）" value="" />
							<el-option
								v-for="category in parentCategories"
								:key="category.id"
								:label="category.categoryName"
								:value="category.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="分类图标" prop="categoryIcon">
						<Image2 v-model="iconUrl" :limit="1"></Image2>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="排序" prop="sortOrder">
						<el-input-number v-model="form.sortOrder" :min="0" :max="9999" placeholder="排序值" style="width: 100%" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio :label="1">启用</el-radio>
							<el-radio :label="0">禁用</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="分类描述" prop="description">
						<el-input type="textarea" v-model="form.description" placeholder="请输入分类描述" :rows="3" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="GoodsCategoryDialog">
import { useMessage } from '/@/hooks/message';
import Image2 from '/@/components/Upload/Image2.vue';

const emit = defineEmits(['refresh']);
const iconUrl = ref<any>([]);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 父级分类数据（模拟数据）
const parentCategories = ref([
	{ id: '1', categoryName: '蔬菜类' },
	{ id: '2', categoryName: '肉类' },
	{ id: '3', categoryName: '海鲜类' },
	{ id: '4', categoryName: '调料类' },
	{ id: '5', categoryName: '豆制品' }
]);

// 提交表单数据
const form = reactive({
	id: '',
	categoryName: '',
	categoryCode: '',
	parentId: '',
	categoryIcon: '',
	sortOrder: 0,
	status: 1,
	description: ''
});

// 定义校验规则
const dataRules = ref({
	categoryName: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
	categoryCode: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }],
	sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
	status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
		// 重置表单默认值
		Object.assign(form, {
			id: '',
			categoryName: '',
			categoryCode: '',
			parentId: '',
			categoryIcon: '',
			sortOrder: 0,
			status: 1,
			description: ''
		});
		iconUrl.value = [];
	});

	// 获取分类信息（编辑时）
	if (id) {
		form.id = id;
		getCategoryData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;

		// 处理图标
		if (iconUrl.value.length) {
			form.categoryIcon = iconUrl.value[0];
		}

		// 模拟提交（实际项目中应该调用API）
		await new Promise(resolve => setTimeout(resolve, 1000));

		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg || '操作失败');
	} finally {
		loading.value = false;
	}
};

// 获取分类数据（编辑时使用）
const getCategoryData = (id: string) => {
	loading.value = true;

	// 模拟获取数据（实际项目中应该调用API）
	setTimeout(() => {
		// 模拟数据
		const mockData = {
			id: id,
			categoryName: '蔬菜类',
			categoryCode: 'VEG001',
			parentId: '',
			categoryIcon: '/images/category/vegetables.png',
			sortOrder: 1,
			status: 1,
			description: '各种新鲜蔬菜'
		};

		Object.assign(form, mockData);
		if (mockData.categoryIcon) {
			iconUrl.value = [mockData.categoryIcon];
		}
		loading.value = false;
	}, 500);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
