<template>
	<el-dialog :title="form.id ? '编辑人员健康信息' : '新增人员健康信息'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="120px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="12" class="mb20">
					<el-form-item label="人员姓名" prop="name">
						<el-input v-model="form.name" placeholder="请输入人员姓名" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="员工编号" prop="employeeId">
						<el-input v-model="form.employeeId" placeholder="请输入员工编号" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="所属部门" prop="department">
						<el-select v-model="form.department" placeholder="请选择所属部门" style="width: 100%">
							<el-option label="食材配送部" value="食材配送部" />
							<el-option label="仓储管理部" value="仓储管理部" />
							<el-option label="质量检验部" value="质量检验部" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="职位" prop="position">
						<el-select v-model="form.position" placeholder="请选择职位" style="width: 100%">
							<el-option label="配送员" value="配送员" />
							<el-option label="配送主管" value="配送主管" />
							<el-option label="仓库管理员" value="仓库管理员" />
							<el-option label="质检员" value="质检员" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="健康状态" prop="healthStatus">
						<el-select v-model="form.healthStatus" placeholder="请选择健康状态" style="width: 100%">
							<el-option label="健康" value="healthy" />
							<el-option label="异常" value="abnormal" />
							<el-option label="待检查" value="pending" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="当日体温(°C)" prop="temperature">
						<el-input-number v-model="form.temperature" :min="35" :max="42" :precision="1" placeholder="请输入体温" style="width: 100%" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="最近体检日期" prop="lastCheckDate">
						<el-date-picker type="date" placeholder="请选择体检日期" v-model="form.lastCheckDate" :value-format="dateStr" style="width: 100%"></el-date-picker>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="下次体检日期" prop="nextCheckDate">
						<el-date-picker type="date" placeholder="请选择下次体检日期" v-model="form.nextCheckDate" :value-format="dateStr" style="width: 100%"></el-date-picker>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="健康证状态" prop="healthCertificate">
						<el-select v-model="form.healthCertificate" placeholder="请选择健康证状态" style="width: 100%">
							<el-option label="有效" value="valid" />
							<el-option label="过期" value="expired" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="健康证到期日期" prop="certificateExpiry">
						<el-date-picker type="date" placeholder="请选择健康证到期日期" v-model="form.certificateExpiry" :value-format="dateStr" style="width: 100%"></el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="HealthDialog">
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['refresh']);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const dateStr = 'YYYY-MM-DD';

// 提交表单数据
const form = reactive({
	id: '',
	name: '',
	employeeId: '',
	department: '',
	position: '',
	healthStatus: '',
	temperature: 36.5,
	lastCheckDate: '',
	nextCheckDate: '',
	healthCertificate: '',
	certificateExpiry: ''
});

// 定义校验规则
const dataRules = ref({
	name: [{ required: true, message: '人员姓名不能为空', trigger: 'blur' }],
	employeeId: [{ required: true, message: '员工编号不能为空', trigger: 'blur' }],
	department: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
	position: [{ required: true, message: '职位不能为空', trigger: 'change' }],
	healthStatus: [{ required: true, message: '健康状态不能为空', trigger: 'change' }],
	temperature: [{ required: true, message: '体温不能为空', trigger: 'blur' }],
	lastCheckDate: [{ required: true, message: '最近体检日期不能为空', trigger: 'change' }],
	nextCheckDate: [{ required: true, message: '下次体检日期不能为空', trigger: 'change' }],
	healthCertificate: [{ required: true, message: '健康证状态不能为空', trigger: 'change' }],
	certificateExpiry: [{ required: true, message: '健康证到期日期不能为空', trigger: 'change' }]
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
		// 重置表单默认值
		Object.assign(form, {
			id: '',
			name: '',
			employeeId: '',
			department: '',
			position: '',
			healthStatus: '',
			temperature: 36.5,
			lastCheckDate: '',
			nextCheckDate: '',
			healthCertificate: '',
			certificateExpiry: ''
		});
	});

	// 如果是编辑模式，模拟获取数据
	if (id) {
		form.id = id;
		getHealthData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		// 模拟提交操作
		await new Promise(resolve => setTimeout(resolve, 1000));
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg || '操作失败');
	} finally {
		loading.value = false;
	}
};

// 模拟获取健康数据
const getHealthData = (id: string) => {
	loading.value = true;
	// 模拟API调用
	setTimeout(() => {
		// 根据ID模拟不同的数据
		const mockData = {
			'1': {
				id: '1',
				name: '张三',
				employeeId: 'EMP001',
				department: '食材配送部',
				position: '配送员',
				healthStatus: 'healthy',
				temperature: 36.5,
				lastCheckDate: '2024-07-15',
				nextCheckDate: '2025-01-15',
				healthCertificate: 'valid',
				certificateExpiry: '2025-03-20'
			},
			'2': {
				id: '2',
				name: '李四',
				employeeId: 'EMP002',
				department: '食材配送部',
				position: '配送主管',
				healthStatus: 'healthy',
				temperature: 36.3,
				lastCheckDate: '2024-07-20',
				nextCheckDate: '2025-01-20',
				healthCertificate: 'valid',
				certificateExpiry: '2025-04-15'
			}
		};

		const data = mockData[id as keyof typeof mockData] || mockData['1'];
		Object.assign(form, data);
		loading.value = false;
	}, 500);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
