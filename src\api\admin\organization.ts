import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/admin/organization/page',
    method: 'get',
    params: query
  })
}

export const list = (params?: Object) => {
  return request({
    url: '/admin/organization/list',
    method: 'get',
    params,
  });
};

export function addObj(obj?: Object) {
  return request({
    url: '/admin/organization',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/admin/organization/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/admin/organization',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/admin/organization',
    method: 'put',
    data: obj
  })
}

