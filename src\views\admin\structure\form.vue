<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="所属机构" prop="organizationId">
						<el-select clearable placeholder="请选择所属机构" v-model="form.organizationId">
							<el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in orgList" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="人员名称" prop="userName">
						<el-input v-model="form.userName" placeholder="请输入人员名称" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="职位职级" prop="position">
						<el-input v-model="form.position" placeholder="请输入职位职级" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="显示层级" prop="level">
						<el-select clearable placeholder="请选择显示层级" v-model="form.level">
							<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in show_level"></el-option>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="排序" prop="sort">
						<el-input-number :min="1" :max="1000" v-model="form.sort" placeholder="请输入排序"></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="OrganizationStructureDialog">
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj } from '/@/api/admin/structure';
import { list as organList } from '/@/api/admin/organization';
import { rule } from '/@/utils/validate';
import { useDict } from '/@/hooks/dict';

const emit = defineEmits(['refresh']);
const { show_level } = useDict('show_level');
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典
const orgList = ref<any[]>([]);
// 提交表单数据
const form = reactive({
	id: '',
	organizationId: '',
	userName: '',
	position: '',
	level: '',
	sort: 0,
});

// 定义校验规则
const dataRules = ref({
	organizationId: [{ required: true, message: '机构ID不能为空', trigger: 'blur' }],
	userName: [{ required: true, message: '人员名称不能为空', trigger: 'blur' }],
	position: [{ required: true, message: '职位职级不能为空', trigger: 'blur' }],
	level: [{ required: true, message: '层级关系不能为空', trigger: 'blur' }],
	sort: [
		{ required: true, message: '排序不能为空', trigger: 'blur' },
		{ validator: rule.number, trigger: 'blur' },
	],
});

// 机构数据
const getOrgList = async () => {
	organList().then((res) => {
		orgList.value = res.data;
	});
};

// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	form.id = '';
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	await getOrgList();
	// 获取organizationStructure信息
	if (id) {
		form.id = id;
		getorganizationStructureData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getorganizationStructureData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
      form.level = res.data.level + '';
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
