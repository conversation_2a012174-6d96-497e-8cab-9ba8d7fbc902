<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="采购单位名称" prop="unitName">
						<el-input placeholder="请输入采购单位名称" v-model="state.queryForm.unitName" />
					</el-form-item>
					<el-form-item label="联系人" prop="contactPerson">
						<el-input placeholder="请输入联系人" v-model="state.queryForm.contactPerson" />
					</el-form-item>
					<el-form-item label="单位类型" prop="unitType">
						<el-select placeholder="请选择单位类型" v-model="state.queryForm.unitType" clearable>
							<el-option label="学校" value="school" />
							<el-option label="企业" value="enterprise" />
							<el-option label="政府机关" value="government" />
							<el-option label="医院" value="hospital" />
							<el-option label="其他" value="other" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
						新 增
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'purchase_unit_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="tableData"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column prop="unitName" label="采购单位名称" show-overflow-tooltip />
				<el-table-column prop="unitType" label="单位类型" width="120">
					<template #default="scope">
						<el-tag :type="getUnitTypeTag(scope.row.unitType)">{{ getUnitTypeText(scope.row.unitType) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="contactPerson" label="联系人" width="120" />
				<el-table-column prop="contactPhone" label="联系电话" width="140" />
				<el-table-column prop="address" label="单位地址" show-overflow-tooltip />
				<el-table-column prop="registrationDate" label="注册时间" width="120" />
				<el-table-column prop="status" label="状态" width="100">
					<template #default="scope">
						<el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
							{{ scope.row.status === 'active' ? '正常' : '停用' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button type="success" link @click="formDialogRef.openDialog(scope.row.id)">编辑</el-button>
						<el-button type="success" link @click="viewDetail(scope.row)">查看详情</el-button>
						<el-button type="success" link @click="handleDelete([scope.row.id])">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="purchaseUnit">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 模拟数据
const tableData = ref([
	{
		id: '1',
		unitName: '阳光小学',
		unitType: 'school',
		contactPerson: '张老师',
		contactPhone: '13800138001',
		address: '江苏省连云港市海州区学府路123号',
		registrationDate: '2023-01-15',
		status: 'active'
	},
	{
		id: '2',
		unitName: '连云港第一中学',
		unitType: 'school',
		contactPerson: '李主任',
		contactPhone: '13800138002',
		address: '江苏省连云港市新浦区教育路456号',
		registrationDate: '2023-02-20',
		status: 'active'
	},
	{
		id: '3',
		unitName: '海州区人民医院',
		unitType: 'hospital',
		contactPerson: '王院长',
		contactPhone: '13800138003',
		address: '江苏省连云港市海州区健康路789号',
		registrationDate: '2023-03-10',
		status: 'active'
	},
	{
		id: '4',
		unitName: '连云港科技有限公司',
		unitType: 'enterprise',
		contactPerson: '刘经理',
		contactPhone: '13800138004',
		address: '江苏省连云港市开发区科技园区101号',
		registrationDate: '2023-04-05',
		status: 'inactive'
	},
	{
		id: '5',
		unitName: '海州区政府机关',
		unitType: 'government',
		contactPerson: '陈主任',
		contactPhone: '13800138005',
		address: '江苏省连云港市海州区政府大楼',
		registrationDate: '2023-05-12',
		status: 'active'
	}
]);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	loading: false,
	pagination: {
		total: tableData.value.length,
		current: 1,
		size: 10
	}
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 获取单位类型标签颜色
const getUnitTypeTag = (type: string) => {
	const tagMap: Record<string, string> = {
		school: 'primary',
		enterprise: 'success',
		government: 'warning',
		hospital: 'info',
		other: 'default'
	};
	return tagMap[type] || 'default';
};

// 获取单位类型文本
const getUnitTypeText = (type: string) => {
	const textMap: Record<string, string> = {
		school: '学校',
		enterprise: '企业',
		government: '政府机关',
		hospital: '医院',
		other: '其他'
	};
	return textMap[type] || '未知';
};

// 查看详情
const viewDetail = (row: any) => {
	useMessage().info(`查看 ${row.unitName} 的详细信息`);
};

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	// 重新加载数据
	state.loading = false;
};

// 导出excel
const exportExcel = () => {
	useMessage().success('导出功能开发中...');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中的采购单位，是否继续？');
	} catch {
		return;
	}

	try {
		// 模拟删除操作
		tableData.value = tableData.value.filter(item => !ids.includes(item.id));
		state.pagination.total = tableData.value.length;
		useMessage().success('删除成功');
		// 清空选择
		selectObjs.value = [];
		multiple.value = true;
	} catch (err: any) {
		useMessage().error('删除失败');
	}
};
</script>
<style lang="scss" scoped>
.layout-padding {

	// 参考供应链页面的样式
	:deep(.el-button--text.el-button--success) {
		color: #00c56b;

		&:hover {
			color: #00a85a;
		}
	}

	:deep(.el-tag--primary) {
		background-color: #ecf5ff;
		border-color: #d9ecff;
		color: #409eff;
	}

	:deep(.el-tag--success) {
		background-color: #f0f9ff;
		border-color: #c6f6d5;
		color: #00c56b;
	}

	:deep(.el-tag--warning) {
		background-color: #fdf6ec;
		border-color: #faecd8;
		color: #e6a23c;
	}

	:deep(.el-tag--info) {
		background-color: #f4f4f5;
		border-color: #e9e9eb;
		color: #909399;
	}

	:deep(.el-tag--danger) {
		background-color: #fef0f0;
		border-color: #fde2e2;
		color: #f56c6c;
	}

	// 表格样式优化
	:deep(.el-table) {
		.el-table__header-wrapper {
			.el-table__header {
				th {
					background-color: #f8f9fa;
					color: #606266;
					font-weight: 600;
				}
			}
		}

		.el-table__body-wrapper {
			.el-table__body {
				tr:hover {
					background-color: #f5f7fa;
				}
			}
		}
	}

	// 搜索表单样式
	.el-form--inline {
		.el-form-item {
			margin-bottom: 10px;
		}
	}
}
</style>
