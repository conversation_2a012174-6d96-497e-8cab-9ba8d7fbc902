<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="订单编号" prop="name">
						<el-input placeholder="请输入订单编号" v-model="state.queryForm.name" />
					</el-form-item>
          <el-form-item label="商品名称" prop="name">
            <el-input placeholder="请输入商品名称" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="采购单位" prop="name">
            <el-input placeholder="请输入采购单位" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="预计送达日期" prop="name">
            <el-date-picker
                v-model="state.queryForm.name"
                type="daterange"
                range-separator="→"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :size="size"
            />
          </el-form-item>
          <el-form-item label="收货人" prop="name">
            <el-input placeholder="请输入收货人" v-model="state.queryForm.name" />
          </el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
      <el-card>
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane v-for="(item,index) in tabList" :label="item.label" :name="item.name" :key="index">
            <el-card v-for="i in 10" :key="i" style="margin-bottom: 20px">
              <div class="demo-tabs-content">
                <el-tag type="success" style="margin-right: 10px">非加工</el-tag>
                <el-tag type="success" style="margin-right: 20px">供应链分拣</el-tag>
                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Tickets /></el-icon>订单编号：<span style="color: #353a3f">202507261735083852</span><el-icon style="color:#04c66e;"><CopyDocument /></el-icon></div>
                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Tickets /></el-icon>配送单位：<span style="color: #353a3f">测试供应商（18210124586）</span><el-icon style="color:#04c66e;"><CopyDocument /></el-icon></div>

                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Clock /></el-icon>预计送达时间：<span style="color: #353a3f">2025-07-29 04:12:12（星期二）</span></div>

                <div style="color: #b6bec5;margin-right: 10px;display: flex;align-items: center"><el-icon><Clock /></el-icon>确认收货时间：<span style="color: #353a3f">2025-07-26 04:12:12（星期六）</span></div>

              </div>

              <div style="display: flex;justify-content: space-between;padding: 10px 20px;align-items: center;border-bottom: 1px solid #dadada">
                <div style="display: flex;align-items: center">
                  <img src="../../../assets/img1.png" style="width: 80px;border-radius: 5px;margin-right: 20px" alt="">
                  <div>
                    <div style="margin-bottom: 10px">五花肉  <span style="color: red">￥12</span></div>
                    <div style="display: flex;align-items: center">
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>订单量 28kg</span>&nbsp;&nbsp;&nbsp;&nbsp;
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>验收量 30kg</span>&nbsp;&nbsp;&nbsp;&nbsp;
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>发货量 30kg</span>
                    </div>
                  </div>
                </div>
                <div style="color: #00c56b">
                  <el-button type="success" text>详情</el-button>
                </div>
              </div>

              <div style="display: flex;padding: 20px 0 0 20px">
                <el-descriptions style="margin-right: 50px">
                  <el-descriptions-item label="下单时间">2025-07-26 04:12:12<</el-descriptions-item>
                  <el-descriptions-item label="用户">李先生</el-descriptions-item>
                  <el-descriptions-item label="收货地址">生态文旅区徐杨路77号仓库中心</el-descriptions-item>
                  <el-descriptions-item label="轨迹">
                    <el-tag size="small" style="color: #00c56b;">点击查看轨迹</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="电话">18210123658</el-descriptions-item>
                </el-descriptions>
                <div style="background: #f8fffc;padding: 10px 20px;display: flex;align-items: center">
                  <div style="margin-right: 20px">
                    <div style="color: #b6bec5;"><el-icon style="margin-right: 10px"><Tickets /></el-icon>应收金额    <span style="color: #353A3FFF">￥336</span></div>
                    <div style="color: #b6bec5;"><el-icon style="margin-right: 10px"><Tickets /></el-icon>实收金额    <span style="color: #353A3FFF">￥336</span></div>
                  </div>

                  <div style="color: #b6bec5;margin-right: 20px"><el-icon style="margin-right: 10px"><Tickets /></el-icon>订单状态    <span style="color: #353A3FFF">完成</span></div>
                  <div style="color: #b6bec5;margin-right: 20px"><el-icon style="margin-right: 10px"><Tickets /></el-icon>结算状态      <span style="color: red">待对账</span></div>

                </div>
              </div>




            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-card>
		</div>

		<!-- 编辑、新增  -->
<!--		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />-->
	</div>
</template>

<script setup lang="ts" name="supplierOrderList">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/admin/organization';
import { useMessage, useMessageBox } from '/@/hooks/message';

const activeName = ref('first')

const tabList = ref([
  {label: '全部（117）', name: 'first'},
  {label: '待供应商发货', name: '1'},
  {label: '待平台验收', name: '2'},
  {label: '分拣中', name: '3'},
  {label: '待平台发货', name: '4'},
  {label: '待配送', name: '5'},
  {label: '配送中', name: '6'},
  {label: '待采购单位验收', name: '7'},
  {label: '交易关闭', name: '8'},
  {label: '订单取消', name: '9'},
  {label: '拒收', name: '10'},
  {label: '完成', name: '11'},
])

// 引入组件
// const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
// const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/organization/export', Object.assign(state.queryForm, { ids: selectObjs }), 'organization.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
<style lang="scss" scoped>
.layout-padding {

  .demo-tabs-content{
    background: #f6fffa;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }

  :deep .el-tabs__item.is-active,:deep .el-tabs__item:hover {
    color: #00c56b;
  }
  :deep .el-tabs__active-bar{
    background-color: #00c56b;
  }
}
</style>
