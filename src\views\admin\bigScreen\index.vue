<template>
  <div class="bigScreen">
    <img src="../../../assets/bg.jpg" style="width: 100%;display: block;" alt="">
    <div class="btn btn1" @click="toWeb('/gps')"></div>
    <div class="btn btn2" @click="toWeb('/monitor')"></div>
  </div>
</template>

<script setup lang="ts" name="bigScreen">

const router = useRouter();

const toWeb = (url: string) => {
    // console.log(url)
  router.push({ path: url})
}

</script>
<style lang="scss" scoped>
.bigScreen{
  width: 100%;
  overflow: auto;
  position: relative;
  padding: 0;
  .btn{
    z-index: 11;
    background: transparent;
    position: absolute;
    width: 220px;
    height: 60px;
    top: 12%;
    left: 61%;
    border: 0;
  }
  .btn2{
    width: 150px;
    left: 39%;
    top: 12%;
  }
}
</style>
