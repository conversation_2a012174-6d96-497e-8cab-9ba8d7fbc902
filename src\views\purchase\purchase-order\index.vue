<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 搜索表单 -->
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="searchOrders">
          <el-form-item label="订单编号" prop="orderNumber">
            <el-input placeholder="请输入订单编号" v-model="state.queryForm.orderNumber" />
          </el-form-item>
          <el-form-item label="商品名称" prop="productName">
            <el-input placeholder="请输入商品名称" v-model="state.queryForm.productName" />
          </el-form-item>
          <el-form-item label="采购单位" prop="purchaseUnit">
            <el-input placeholder="请输入采购单位" v-model="state.queryForm.purchaseUnit" />
          </el-form-item>
          <el-form-item label="预计送达日期" prop="deliveryDate">
            <el-date-picker v-model="state.queryForm.deliveryDate" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item label="收货人" prop="receiver">
            <el-input placeholder="请输入收货人" v-model="state.queryForm.receiver" />
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="searchOrders">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>

      <!-- 主要内容卡片 -->
      <el-card>
        <!-- 标签页 -->
        <el-tabs v-model="activeStatus" class="demo-tabs">
          <el-tab-pane v-for="tab in statusTabs" :key="tab.value" :label="tab.label" :name="tab.value">
            <!-- 订单卡片示例 -->
            <el-card v-for="order in filteredOrders" :key="order.id" style="margin-bottom: 20px">
              <!-- 订单头部信息 -->
              <div class="demo-tabs-content">
                <el-tag type="success" style="margin-right: 10px">{{ order.orderType || "非加工" }}</el-tag>
                <el-tag type="success" style="margin-right: 20px">{{ order.processType || "供应链分拣" }}</el-tag>
                <div style="color: #b6bec5; margin-right: 20px; display: flex; align-items: center">
                  <el-icon><Tickets /></el-icon>订单编号：<span style="color: #353a3f">{{ order.orderNumber }}</span>
                  <el-icon style="color: #04c66e"><CopyDocument /></el-icon>
                </div>
                <div style="color: #b6bec5; margin-right: 20px; display: flex; align-items: center">
                  <el-icon><Tickets /></el-icon>配送单位：<span style="color: #353a3f">{{ order.deliveryUnit || "测试供应商（18210124586）" }}</span>
                  <el-icon style="color: #04c66e"><CopyDocument /></el-icon>
                </div>
                <div style="color: #b6bec5; margin-right: 20px; display: flex; align-items: center">
                  <el-icon><Clock /></el-icon>预计送达时间：<span style="color: #353a3f">{{ order.deliveryDate }} {{ order.deliveryPeriod }}（星期二）</span>
                </div>
                <div style="color: #b6bec5; margin-right: 10px; display: flex; align-items: center">
                  <el-icon><Clock /></el-icon>确认收货时间：<span style="color: #353a3f">{{ order.confirmTime || "2025-07-26 04:12:12（星期六）" }}</span>
                </div>
              </div>

              <!-- 商品信息 -->
              <div style="display: flex; justify-content: space-between; padding: 10px 20px; align-items: center; border-bottom: 1px solid #dadada">
                <div style="display: flex; align-items: center">
                  <img :src="order.productImage" style="width: 80px; border-radius: 5px; margin-right: 20px" alt="" />
                  <div>
                    <div style="margin-bottom: 10px">
                      {{ order.productName }} <span style="color: red">￥{{ order.unitPrice }}</span>
                    </div>
                    <div style="display: flex; align-items: center">
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>订单量 {{ order.orderWeight || order.weight }}</span
                      >&nbsp;&nbsp;&nbsp;&nbsp; <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>验收量 {{ order.acceptWeight || order.weight }}</span
                      >&nbsp;&nbsp;&nbsp;&nbsp; <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>发货量 {{ order.shipWeight || order.weight }}</span>
                    </div>
                  </div>
                </div>
                <div style="color: #00c56b">
                  <el-button type="success" text @click="showDetail(123)">详情</el-button>
                </div>
              </div>

              <!-- 订单详情 -->
              <div style="display: flex; padding: 20px 0 0 20px">
                <el-descriptions style="margin-right: 50px">
                  <el-descriptions-item label="下单时间">{{ order.orderTime }}</el-descriptions-item>
                  <el-descriptions-item label="用户">{{ order.buyer }}</el-descriptions-item>
                  <el-descriptions-item label="收货地址">{{ order.deliveryAddress }}</el-descriptions-item>
                  <el-descriptions-item label="轨迹">
                    <el-tag size="small" style="color: #00c56b">点击查看轨迹</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="电话">{{ order.phone }}</el-descriptions-item>
                </el-descriptions>
                <div style="background: #f8fffc; padding: 10px 20px; display: flex; align-items: center">
                  <div style="margin-right: 20px">
                    <div style="color: #b6bec5">
                      <el-icon style="margin-right: 10px"><Tickets /></el-icon>应收金额 <span style="color: #353a3fff">￥{{ order.actualAmount }}</span>
                    </div>
                    <div style="color: #b6bec5">
                      <el-icon style="margin-right: 10px"><Tickets /></el-icon>实收金额 <span style="color: #353a3fff">￥{{ order.actualAmount }}</span>
                    </div>
                  </div>
                  <div style="color: #b6bec5; margin-right: 20px">
                    <el-icon style="margin-right: 10px"><Tickets /></el-icon>订单状态 <span style="color: #353a3fff">{{ getStatusText(order.status) }}</span>
                  </div>
                  <div style="color: #b6bec5; margin-right: 20px">
                    <el-icon style="margin-right: 10px"><Tickets /></el-icon>结算状态 <span style="color: red">{{ order.settlementStatus || "待对账" }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts" name="purchaseOrder">
import { BasicTableProps } from "/@/hooks/table";
import { useMessage } from "/@/hooks/message";
import { Clock, Tickets, CopyDocument } from "@element-plus/icons-vue";
import IMG1 from '/@/assets/img1.png';

const router = useRouter();

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 当前状态
const activeStatus = ref("all");

// 状态标签
const statusTabs = ref([
  { label: "全部（59）", value: "all" },
  { label: "备货中", value: "preparing" },
  { label: "待配送", value: "pending_delivery" },
  { label: "配送中", value: "delivering" },
  { label: "待客户确认收货", value: "pending_confirm" },
  { label: "交易关闭", value: "closed" },
  { label: "订单取消", value: "cancelled" },
  { label: "拒收", value: "rejected" },
  { label: "完成", value: "completed" },
]);

// 模拟订单数据
const orderData = ref([
  {
    id: "1",
    status: "preparing",
    orderNumber: "20250726175268414",
    orderTime: "2025-07-26 15:30:00",
    deliveryDate: "2025-07-29",
    deliveryPeriod: "晚餐一",
    supplierOrderTime: "2025-07-26 17:35:00",
    productImage: IMG1,
    productName: "五花肉",
    unitPrice: "15",
    weight: "60kg",
    buyer: "客户A",
    deliveryAddress: "217",
    phone: "13333333333",
    contactPhone: "13333333333",
    actualAmount: "1320",
    currentStatus: "备货中",
    deliveryAvatars: [
      { id: 1, src: "https://via.placeholder.com/24x24/409EFF/ffffff?text=A" },
      { id: 2, src: "https://via.placeholder.com/24x24/67C23A/ffffff?text=B" },
    ],
  },
  {
    id: "2",
    status: "pending_delivery",
    orderNumber: "20250726175268460",
    orderTime: "2025-07-26 06:30:00",
    deliveryDate: "2025-07-29",
    deliveryPeriod: "晚餐一",
    supplierOrderTime: "2025-07-26 17:35:00",
    productImage: IMG1,
    productName: "五花肉",
    unitPrice: "15",
    weight: "24kg",
    buyer: "客户A",
    deliveryAddress: "217",
    phone: "13333333333",
    contactPhone: "13333333333",
    actualAmount: "360",
    currentStatus: "待客户确认收货",
    deliveryAvatars: [{ id: 1, src: "https://via.placeholder.com/24x24/409EFF/ffffff?text=C" }],
  },
  {
    id: "3",
    status: "delivering",
    orderNumber: "20250726175268407",
    orderTime: "2025-07-26 06:30:00",
    deliveryDate: "2025-07-29",
    deliveryPeriod: "晚餐一",
    supplierOrderTime: "2025-07-26 17:35:00",
    productImage: IMG1,
    productName: "五花肉",
    unitPrice: "15",
    weight: "0kg",
    buyer: "客户A",
    deliveryAddress: "217",
    phone: "13333333333",
    contactPhone: "13333333333",
    actualAmount: "450",
    currentStatus: "备货中",
    deliveryAvatars: [
      { id: 1, src: "https://via.placeholder.com/24x24/409EFF/ffffff?text=D" },
      { id: 2, src: "https://via.placeholder.com/24x24/67C23A/ffffff?text=E" },
      { id: 3, src: "https://via.placeholder.com/24x24/E6A23C/ffffff?text=F" },
    ],
  },
]);

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  loading: false,
  pagination: {
    total: orderData.value.length,
    current: 1,
    size: 10,
  },
});

// 根据状态过滤订单
const filteredOrders = computed(() => {
  if (activeStatus.value === "all") {
    return orderData.value;
  }
  return orderData.value.filter((order) => order.status === activeStatus.value);
});

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    preparing: "备货中",
    pending_delivery: "待配送",
    delivering: "配送中",
    pending_confirm: "待客户确认收货",
    closed: "交易关闭",
    cancelled: "订单取消",
    completed: "完成",
  };
  return statusMap[status] || "未知状态";
};

// 搜索订单
const searchOrders = () => {
  // 这里可以添加搜索逻辑
  useMessage().info("搜索功能开发中...");
};

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields();
  state.queryForm = {};
  searchOrders();
};

const showDetail = (id: number) => {
  router.push(`/purchase/purchase-order/detail?id=${id}`);
};
</script>
<style lang="scss" scoped>
.layout-padding {
  .demo-tabs-content {
    background: #f6fffa;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }

  :deep(.el-tabs__item.is-active),
  :deep(.el-tabs__item:hover) {
    color: #00c56b;
  }

  :deep(.el-tabs__active-bar) {
    background-color: #00c56b;
  }
}
</style>
