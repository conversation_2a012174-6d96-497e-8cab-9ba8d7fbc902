import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/admin/sysComplaint/page',
    method: 'get',
    params: query
  })
}
export function fetchList1(query?: Object) {
  return request({
    url: '/sysDealProcesssys/page',
    method: 'get',
    params: query
  })
}
export function sysDealProcessList(query?: Object) {
  return request({
    url: '/admin/sysDealProcess/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/admin/sysComplaint',
    method: 'post',
    data: obj
  })
}

export function addAssignedObj(obj?: Object) {
  return request({
    url: '/admin/sysDealProcess/assign',
    method: 'post',
    data: obj
  })
}
export function addAuditObj(obj?: Object) {
  return request({
    url: '/admin/sysDealProcess/audit',
    method: 'post',
    data: obj
  })
}
export function addAuditDealObj(obj?: Object) {
  return request({
    url: '/admin/sysDealProcess/auditDeal',
    method: 'post',
    data: obj
  })
}
export function addMultiAssignedObj(obj?: Object) {
  return request({
    url: '/admin/sysDealProcess/multiAssign',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/admin/sysComplaint/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/admin/sysComplaint',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/admin/sysComplaint',
    method: 'put',
    data: obj
  })
}

