<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="分类名称" prop="categoryName">
            <el-input placeholder="请输入分类名称" v-model="state.queryForm.categoryName" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select placeholder="请选择状态" v-model="state.queryForm.status" clearable>
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'goods_category_add'"> 新 增 </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'goods_category_del'" @click="handleDelete(selectObjs)"> 删除 </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'goods_category_export'" @exportExcel="exportExcel" class="ml10 mr20" style="float: right" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table
        :data="state.dataList"
        v-loading="state.loading"
        border
        :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle"
        @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column prop="categoryName" label="分类名称" show-overflow-tooltip />
        <el-table-column prop="categoryCode" label="分类编码" show-overflow-tooltip />
        <el-table-column prop="categoryIcon" label="分类图标" width="100" align="center">
          <template #default="scope">
            <div class="icon-box" v-if="scope.row.categoryIcon">
              <!-- <el-image
                :src="scope.row.categoryIcon.includes('http') ? scope.row.categoryIcon : baseURL + scope.row.categoryIcon"
                :preview-src-list="[scope.row.categoryIcon.includes('http') ? scope.row.categoryIcon : baseURL + scope.row.categoryIcon]"
                fit="cover"
                :preview-teleported="true"
                :hide-on-click-modal="true"
                style="width: 40px; height: 40px"
              ></el-image> -->
              <el-image
                :src="scope.row.categoryIcon"
                :preview-src-list="[scope.row.categoryIcon]"
                fit="cover"
                :preview-teleported="true"
                :hide-on-click-modal="true"
                style="width: 40px; height: 40px"
              ></el-image>
            </div>
            <div class="noPic" v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="parentCategoryName" label="父级分类" show-overflow-tooltip />
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.id)">编辑 </el-button>
            <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="goodsCategory">
import { useMessage, useMessageBox } from "/@/hooks/message";
import IMG1 from '/@/assets/img1.png';
import IMG3 from '/@/assets/img3.jpg';
// import IMG4 from '/@/assets/img4.png';
// import IMG5 from '/@/assets/img5.png';
import IMG6 from '/@/assets/img6.png';
import IMG7 from '/@/assets/img7.png';
import IMG8 from '/@/assets/img8.png';
import IMG9 from '/@/assets/img9.png';
import IMG10 from '/@/assets/img10.png';
import IMG11 from '/@/assets/img11.png';
import IMG12 from '/@/assets/img12.png';

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 写死的食材商品分类数据
const mockCategoryData = [
  {
    id: "1",
    categoryName: "蔬菜类",
    categoryCode: "VEG001",
    categoryIcon: IMG3,
    parentCategoryName: "-",
    sortOrder: 1,
    status: 1,
    description: "各种新鲜蔬菜",
    createTime: "2024-01-15 10:30:00",
  },
  {
    id: "2",
    categoryName: "肉蛋奶类",
    categoryCode: "MEAT001",
    categoryIcon: IMG1,
    parentCategoryName: "-",
    sortOrder: 2,
    status: 1,
    description: "新鲜肉类食材",
    createTime: "2024-01-15 10:35:00",
  },
  {
    id: "3",
    categoryName: "海鲜类",
    categoryCode: "SEA001",
    categoryIcon: IMG9,
    parentCategoryName: "-",
    sortOrder: 3,
    status: 1,
    description: "新鲜海鲜产品",
    createTime: "2024-01-15 10:40:00",
  },
  {
    id: "4",
    categoryName: "调料类",
    categoryCode: "SPICE001",
    categoryIcon: IMG7,
    parentCategoryName: "-",
    sortOrder: 4,
    status: 1,
    description: "各种调味料和香料",
    createTime: "2024-01-15 10:45:00",
  },
  {
    id: "5",
    categoryName: "豆制品",
    categoryCode: "BEAN001",
    categoryIcon: IMG8,
    parentCategoryName: "-",
    sortOrder: 5,
    status: 1,
    description: "豆腐、豆干等豆制品",
    createTime: "2024-01-15 10:50:00",
  },
  {
    id: "6",
    categoryName: "根茎类",
    categoryCode: "VEG002",
    categoryIcon: IMG6,
    parentCategoryName: "蔬菜类",
    sortOrder: 6,
    status: 1,
    description: "土豆、萝卜等根茎类蔬菜",
    createTime: "2024-01-15 11:00:00",
  },
  {
    id: "7",
    categoryName: "叶菜类",
    categoryCode: "VEG003",
    categoryIcon: IMG10,
    parentCategoryName: "蔬菜类",
    sortOrder: 7,
    status: 1,
    description: "青菜、菠菜等叶菜类",
    createTime: "2024-01-15 11:05:00",
  },
  {
    id: "8",
    categoryName: "水果类",
    categoryCode: "VEG009",
    categoryIcon: IMG12,
    parentCategoryName: "-",
    sortOrder: 8,
    status: 1,
    description: "水果",
    createTime: "2024-01-15 11:05:00",
  },
  {
    id: "9",
    categoryName: "冷冻食品",
    categoryCode: "FROZEN001",
    categoryIcon: IMG11,
    parentCategoryName: "-",
    sortOrder: 9,
    status: 1,
    description: "各种冷冻食材",
    createTime: "2024-01-15 11:10:00",
  },
];

const state = reactive({
  queryForm: {
    categoryName: "",
    status: "",
  },
  dataList: [] as any[],
  loading: false,
  pagination: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
  },
});

// 表格样式
const tableStyle = {
  cellStyle: { textAlign: "center" },
  headerCellStyle: { textAlign: "center", background: "#fafafa" },
};

// 获取数据列表
const getDataList = (loading = true) => {
  if (loading) state.loading = true;

  // 模拟异步请求
  setTimeout(() => {
    let filteredData = [...mockCategoryData];

    // 根据查询条件过滤数据
    if (state.queryForm.categoryName) {
      filteredData = filteredData.filter((item) => item.categoryName.includes(state.queryForm.categoryName));
    }

    if (state.queryForm.status !== "") {
      filteredData = filteredData.filter((item) => item.status === Number(state.queryForm.status));
    }

    // 分页处理
    const start = (state.pagination.currentPage - 1) * state.pagination.pageSize;
    const end = start + state.pagination.pageSize;

    state.dataList = filteredData.slice(start, end);
    state.pagination.total = filteredData.length;
    state.loading = false;
  }, 500);
};

// 页面大小改变
const sizeChangeHandle = (size: number) => {
  state.pagination.pageSize = size;
  state.pagination.currentPage = 1;
  getDataList();
};

// 当前页改变
const currentChangeHandle = (current: number) => {
  state.pagination.currentPage = current;
  getDataList();
};

// 排序改变
const sortChangeHandle = () => {
  getDataList();
};

// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  selectObjs.value = [];
  getDataList();
};

// 导出excel
const exportExcel = () => {
  useMessage().info("导出功能暂未实现");
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm("此操作将永久删除选中的分类，是否继续？");
  } catch {
    return;
  }

  // 模拟删除操作
  useMessage().success(`成功删除 ${ids.length} 个分类`);
  getDataList();
};

// 初始化数据
onMounted(() => {
  getDataList();
});
</script>
<style lang="scss" scoped>
.icon-box {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }
}

.noPic {
  color: #999;
  font-size: 14px;
}
</style>
