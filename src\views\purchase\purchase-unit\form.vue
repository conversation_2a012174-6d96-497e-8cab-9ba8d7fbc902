<template>
	<el-dialog :title="form.id ? '编辑采购单位' : '新增采购单位'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="120px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="采购单位名称" prop="unitName">
						<el-input v-model="form.unitName" placeholder="请输入采购单位名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="单位类型" prop="unitType">
						<el-select v-model="form.unitType" placeholder="请选择单位类型" style="width: 100%">
							<el-option label="学校" value="school" />
							<el-option label="企业" value="enterprise" />
							<el-option label="政府机关" value="government" />
							<el-option label="医院" value="hospital" />
							<el-option label="其他" value="other" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="状态" prop="status">
						<el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
							<el-option label="正常" value="active" />
							<el-option label="停用" value="inactive" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="联系人" prop="contactPerson">
						<el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="联系电话" prop="contactPhone">
						<el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="单位地址" prop="address">
						<el-input v-model="form.address" placeholder="请输入单位详细地址" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="注册时间" prop="registrationDate">
						<el-date-picker
							type="date"
							placeholder="请选择注册时间"
							v-model="form.registrationDate"
							:value-format="dateStr"
							style="width: 100%"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="统一社会信用代码" prop="creditCode">
						<el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="单位简介" prop="description">
						<el-input
							type="textarea"
							v-model="form.description"
							placeholder="请输入单位简介"
							:rows="3"
						/>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="备注" prop="remark">
						<el-input
							type="textarea"
							v-model="form.remark"
							placeholder="请输入备注信息"
							:rows="2"
						/>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="PurchaseUnitDialog">
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 定义字典
const dateStr = 'YYYY-MM-DD';

// 提交表单数据
const form = reactive({
	id: '',
	unitName: '',
	unitType: '',
	contactPerson: '',
	contactPhone: '',
	address: '',
	registrationDate: '',
	creditCode: '',
	description: '',
	status: 'active',
	remark: ''
});

// 定义校验规则
const dataRules = ref({
	unitName: [{ required: true, message: '采购单位名称不能为空', trigger: 'blur' }],
	unitType: [{ required: true, message: '单位类型不能为空', trigger: 'change' }],
	contactPerson: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
	contactPhone: [
		{ required: true, message: '联系电话不能为空', trigger: 'blur' },
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
	],
	address: [{ required: true, message: '单位地址不能为空', trigger: 'blur' }],
	registrationDate: [{ required: true, message: '注册时间不能为空', trigger: 'change' }],
	status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
});

// 打开弹窗
const openDialog = (id?: string) => {
	visible.value = true;

	// 重置表单数据
	Object.assign(form, {
		id: '',
		unitName: '',
		unitType: '',
		contactPerson: '',
		contactPhone: '',
		address: '',
		registrationDate: '',
		creditCode: '',
		description: '',
		status: 'active',
		remark: ''
	});

	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 如果是编辑模式，模拟获取数据
	if (id) {
		form.id = id;
		getPurchaseUnitData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;

		// 模拟API调用
		await new Promise(resolve => setTimeout(resolve, 1000));

		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error('操作失败');
	} finally {
		loading.value = false;
	}
};

// 模拟获取采购单位数据
const getPurchaseUnitData = (id: string) => {
	loading.value = true;

	// 模拟数据
	const mockData = {
		'1': {
			id: '1',
			unitName: '阳光小学',
			unitType: 'school',
			contactPerson: '张老师',
			contactPhone: '13800138001',
			address: '江苏省连云港市海州区学府路123号',
			registrationDate: '2023-01-15',
			creditCode: '12320000MB1234567X',
			description: '一所优秀的小学，致力于为学生提供优质的教育服务。',
			status: 'active',
			remark: '重点合作单位'
		},
		'2': {
			id: '2',
			unitName: '连云港第一中学',
			unitType: 'school',
			contactPerson: '李主任',
			contactPhone: '13800138002',
			address: '江苏省连云港市新浦区教育路456号',
			registrationDate: '2023-02-20',
			creditCode: '12320000MB2345678Y',
			description: '连云港市重点中学，教学质量优秀。',
			status: 'active',
			remark: ''
		}
	};

	setTimeout(() => {
		const data = mockData[id as keyof typeof mockData];
		if (data) {
			Object.assign(form, data);
		}
		loading.value = false;
	}, 500);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
