<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="用户姓名" prop="userId">
						<el-select v-model="form.userId" placeholder="请选择用户" size="large">
							<el-option v-for="item in users" :key="item.userId" :label="item.nickname" :value="item.userId" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="年份" prop="year">
						<el-date-picker type="year" placeholder="请输入年份" v-model="form.year" format="YYYY" value-format="YYYY"></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="24" class="mb20">
					<el-form-item label="报告文件" prop="fileUrl">
						<UploadFile v-model="form.fileUrl" :limit="1"></UploadFile>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="UserAnnualReportDialog">
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj } from '/@/api/admin/annualReport';
import { listAll as userList } from '/@/api/admin/user';
import UploadFile from '/@/components/Upload/UploadFile.vue';

const emit = defineEmits(['refresh']);
const users = ref();
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典

// 提交表单数据
const form = reactive({
	id: '',
	userId: '',
	year: '',
	fileUrl: '',
});

// 定义校验规则
const dataRules = ref({
	userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
	year: [{ required: true, message: '年份不能为空', trigger: 'blur' }],
	fileUrl: [{ required: true, message: '报告文件不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	form.id = '';
	const res = await userList();
	users.value = res.data;
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 获取userAnnualReport信息
	if (id) {
		form.id = id;
		getuserAnnualReportData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getuserAnnualReportData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
			form.year = res.data.year.toString();
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
